/**
 * TutorAI Extension Popup
 * Handles user interactions and communicates with content script
 */

class TutorAIPopup {
  constructor() {
    this.currentTab = null;
    this.isHighlightingEnabled = false;
    this.settings = {};
    this.pendingUpdate = null;

    this.init();
  }

  async init() {
    // Get current tab
    this.currentTab = await this.getCurrentTab();

    // Load settings
    await this.loadSettings();

    // Check for pending updates
    await this.checkPendingUpdate();

    // Update UI
    this.updatePageInfo();
    this.updateStatus();

    // Set up event listeners
    this.setupEventListeners();

    // Check connection status
    this.checkConnectionStatus();
  }

  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab;
  }

  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
      this.settings = response || {};

      // Update UI with settings
      this.updateSettingsUI();

    } catch (error) {
      console.error('Failed to load settings:', error);
      // Use defaults if loading fails
      this.settings = {
        enabled: true,
        autoStart: false,
        voiceEnabled: false,
        highlightColor: '#3b82f6',
        tutorAIServer: 'http://localhost:3000'
      };
      this.updateSettingsUI();
    }
  }

  updateSettingsUI() {
    // Basic settings
    const autoStartEl = document.getElementById('auto-start');
    const voiceEnabledEl = document.getElementById('voice-enabled');
    const highlightColorEl = document.getElementById('highlight-color');

    if (autoStartEl) autoStartEl.checked = this.settings.autoStart || false;
    if (voiceEnabledEl) voiceEnabledEl.checked = this.settings.voiceEnabled || false;
    if (highlightColorEl) highlightColorEl.value = this.settings.highlightColor || '#3b82f6';
  }

  async saveSettings() {
    try {
      await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        settings: this.settings
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  }

  async updateSetting(key, value) {
    try {
      await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTING',
        key: key,
        value: value
      });

      // Update local settings
      this.settings[key] = value;

    } catch (error) {
      console.error('Failed to update setting:', error);
      throw error;
    }
  }

  updatePageInfo() {
    if (this.currentTab) {
      document.getElementById('page-title').textContent = this.currentTab.title || 'Unknown Page';
      document.getElementById('page-url').textContent = this.currentTab.url || 'Unknown URL';
    }
  }

  updateStatus(status = 'Ready', isConnected = true) {
    const indicator = document.getElementById('status-indicator');
    const statusText = indicator.querySelector('.status-text');
    const statusDot = indicator.querySelector('.status-dot');
    
    statusText.textContent = status;
    statusDot.className = `status-dot ${isConnected ? 'connected' : 'disconnected'}`;
  }

  setupEventListeners() {
    // Quick action buttons
    document.getElementById('start-tutorial').addEventListener('click', () => {
      this.startTutorial();
    });

    document.getElementById('explain-page').addEventListener('click', () => {
      this.explainPage();
    });

    document.getElementById('toggle-highlighting').addEventListener('click', () => {
      this.toggleHighlighting();
    });

    // Settings
    document.getElementById('auto-start').addEventListener('change', async (e) => {
      try {
        await this.updateSetting('autoStart', e.target.checked);
      } catch (error) {
        // Revert UI on error
        e.target.checked = !e.target.checked;
        this.showError('Failed to update auto-start setting');
      }
    });

    document.getElementById('voice-enabled').addEventListener('change', async (e) => {
      try {
        await this.updateSetting('voiceEnabled', e.target.checked);
      } catch (error) {
        // Revert UI on error
        e.target.checked = !e.target.checked;
        this.showError('Failed to update voice setting');
      }
    });

    document.getElementById('highlight-color').addEventListener('change', async (e) => {
      try {
        await this.updateSetting('highlightColor', e.target.value);
      } catch (error) {
        // Revert UI on error
        e.target.value = this.settings.highlightColor;
        this.showError('Failed to update highlight color');
      }
    });

    // Response actions
    document.getElementById('clear-response').addEventListener('click', () => {
      this.clearResponse();
    });

    // Advanced settings
    document.getElementById('advanced-settings').addEventListener('click', () => {
      this.showAdvancedSettings();
    });

    document.getElementById('backup-settings').addEventListener('click', () => {
      this.showBackupManager();
    });

    document.getElementById('reset-settings').addEventListener('click', () => {
      this.confirmResetSettings();
    });

    // Footer links
    document.getElementById('open-dashboard').addEventListener('click', () => {
      this.openDashboard();
    });

    document.getElementById('help').addEventListener('click', () => {
      this.openHelp();
    });

    document.getElementById('feedback').addEventListener('click', () => {
      this.openFeedback();
    });
  }

  async sendMessageToContentScript(message) {
    if (!this.currentTab) return;

    try {
      await chrome.tabs.sendMessage(this.currentTab.id, message);
    } catch (error) {
      console.error('Failed to send message to content script:', error);
      this.updateStatus('Error: Content script not loaded', false);
    }
  }

  async startTutorial() {
    this.showLoading('Starting tutorial...');
    
    await this.sendMessageToContentScript({
      type: 'START_TUTORIAL',
      data: {
        query: 'Please provide a comprehensive tutorial for this webpage'
      }
    });
    
    this.hideLoading();
    this.updateStatus('Tutorial started');
    window.close(); // Close popup after starting tutorial
  }

  async explainPage() {
    this.showLoading('Analyzing page...');
    
    await this.sendMessageToContentScript({
      type: 'START_TUTORIAL',
      data: {
        query: 'Please explain what this webpage is for and how to use it effectively'
      }
    });
    
    this.hideLoading();
    this.updateStatus('Explaining page');
    window.close();
  }

  async toggleHighlighting() {
    this.isHighlightingEnabled = !this.isHighlightingEnabled;
    
    const button = document.getElementById('toggle-highlighting');
    const text = document.getElementById('highlight-text');
    
    if (this.isHighlightingEnabled) {
      text.textContent = 'Disable Highlighting';
      button.classList.add('active');
      this.updateStatus('Highlighting enabled');
    } else {
      text.textContent = 'Enable Highlighting';
      button.classList.remove('active');
      this.updateStatus('Highlighting disabled');
    }
    
    await this.sendMessageToContentScript({
      type: 'TOGGLE_HIGHLIGHTING',
      enabled: this.isHighlightingEnabled
    });
  }

  async checkConnectionStatus() {
    try {
      const response = await fetch(`${this.settings.tutorAIServer}/api/ag-ui/stream`);
      if (response.ok) {
        this.updateStatus('Connected', true);
      } else {
        this.updateStatus('Server unavailable', false);
      }
    } catch (error) {
      this.updateStatus('Connection failed', false);
    }
  }

  showResponse(text) {
    const responseSection = document.getElementById('response-section');
    const responseText = document.getElementById('response-text');
    
    responseText.textContent = text;
    responseSection.style.display = 'block';
  }

  clearResponse() {
    const responseSection = document.getElementById('response-section');
    const responseText = document.getElementById('response-text');
    
    responseText.textContent = '';
    responseSection.style.display = 'none';
  }

  showLoading(text = 'Loading...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = overlay.querySelector('.loading-text');
    
    loadingText.textContent = text;
    overlay.style.display = 'flex';
  }

  hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    overlay.style.display = 'none';
  }

  openDashboard() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/dashboard`
    });
  }

  openHelp() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/help`
    });
  }

  openFeedback() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/feedback`
    });
  }

  // ===== UPDATE MANAGEMENT =====

  async checkPendingUpdate() {
    try {
      const result = await chrome.storage.local.get(['pendingUpdate']);
      this.pendingUpdate = result.pendingUpdate;

      if (this.pendingUpdate) {
        this.showUpdateNotification();
      }
    } catch (error) {
      console.error('Failed to check pending update:', error);
    }
  }

  showUpdateNotification() {
    if (!this.pendingUpdate) return;

    // Create update notification element
    const updateNotification = document.createElement('div');
    updateNotification.className = 'update-notification';
    updateNotification.innerHTML = `
      <div class="update-header">
        <span class="update-icon">🔄</span>
        <span class="update-title">Update Available</span>
        ${this.pendingUpdate.isSecurityUpdate ? '<span class="security-badge">Security</span>' : ''}
      </div>
      <div class="update-content">
        <p>Version ${this.pendingUpdate.version} is ready to install.</p>
        <div class="update-actions">
          <button id="install-update" class="btn btn-primary btn-small">Install Now</button>
          <button id="view-changelog" class="btn btn-secondary btn-small">View Changes</button>
          <button id="dismiss-update" class="btn btn-text btn-small">Later</button>
        </div>
      </div>
    `;

    // Insert at the top of content
    const content = document.querySelector('.content');
    content.insertBefore(updateNotification, content.firstChild);

    // Add event listeners
    document.getElementById('install-update').addEventListener('click', () => {
      this.installUpdate();
    });

    document.getElementById('view-changelog').addEventListener('click', () => {
      this.showChangelog();
    });

    document.getElementById('dismiss-update').addEventListener('click', () => {
      this.dismissUpdate();
    });
  }

  async installUpdate() {
    try {
      // Show loading state
      const installButton = document.getElementById('install-update');
      installButton.textContent = 'Installing...';
      installButton.disabled = true;

      // Send message to background script to initiate update
      await chrome.runtime.sendMessage({
        type: 'INSTALL_UPDATE',
        data: this.pendingUpdate
      });

    } catch (error) {
      console.error('Failed to install update:', error);

      // Reset button state
      const installButton = document.getElementById('install-update');
      installButton.textContent = 'Install Now';
      installButton.disabled = false;

      // Show error message
      this.showError('Failed to install update. Please try again.');
    }
  }

  showChangelog() {
    // Create changelog modal
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>What's New in v${this.pendingUpdate.version}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="changelog-content">
            ${this.pendingUpdate.releaseNotes}
          </div>
        </div>
        <div class="modal-footer">
          <button id="modal-install" class="btn btn-primary">Install Update</button>
          <button id="modal-close" class="btn btn-secondary">Close</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    modal.querySelector('.modal-close').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#modal-close').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#modal-install').addEventListener('click', () => {
      document.body.removeChild(modal);
      this.installUpdate();
    });

    // Close on overlay click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  async dismissUpdate() {
    try {
      // Remove update notification from UI
      const notification = document.querySelector('.update-notification');
      if (notification) {
        notification.remove();
      }

      // Clear pending update (but keep it in storage for later)
      this.pendingUpdate = null;

    } catch (error) {
      console.error('Failed to dismiss update:', error);
    }
  }

  showError(message) {
    // Create error notification
    const errorNotification = document.createElement('div');
    errorNotification.className = 'error-notification';
    errorNotification.innerHTML = `
      <div class="error-content">
        <span class="error-icon">⚠️</span>
        <span class="error-message">${message}</span>
        <button class="error-close">&times;</button>
      </div>
    `;

    document.querySelector('.content').appendChild(errorNotification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorNotification.parentNode) {
        errorNotification.parentNode.removeChild(errorNotification);
      }
    }, 5000);

    // Manual close
    errorNotification.querySelector('.error-close').addEventListener('click', () => {
      if (errorNotification.parentNode) {
        errorNotification.parentNode.removeChild(errorNotification);
      }
    });
  }

  // ===== ADVANCED SETTINGS =====

  showAdvancedSettings() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content advanced-settings-modal">
        <div class="modal-header">
          <h3>Advanced Settings</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="advanced-settings">
            <div class="setting-group">
              <h4>Performance</h4>
              <label class="setting-item">
                <span>Animation Speed:</span>
                <select id="animation-speed">
                  <option value="slow">Slow</option>
                  <option value="normal">Normal</option>
                  <option value="fast">Fast</option>
                </select>
              </label>
              <label class="setting-item">
                <span>Tutorial Delay (ms):</span>
                <input type="number" id="tutorial-delay" min="0" max="10000" step="100" />
              </label>
              <label class="setting-item">
                <input type="checkbox" id="preload-content" />
                <span>Preload content</span>
              </label>
            </div>

            <div class="setting-group">
              <h4>Privacy</h4>
              <label class="setting-item">
                <input type="checkbox" id="analytics-enabled" />
                <span>Enable analytics</span>
              </label>
              <label class="setting-item">
                <input type="checkbox" id="error-reporting" />
                <span>Error reporting</span>
              </label>
              <label class="setting-item">
                <input type="checkbox" id="usage-data-sharing" />
                <span>Usage data sharing</span>
              </label>
            </div>

            <div class="setting-group">
              <h4>Accessibility</h4>
              <label class="setting-item">
                <input type="checkbox" id="high-contrast" />
                <span>High contrast mode</span>
              </label>
              <label class="setting-item">
                <input type="checkbox" id="reduced-motion" />
                <span>Reduced motion</span>
              </label>
              <label class="setting-item">
                <span>Font Size:</span>
                <select id="font-size">
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="save-advanced" class="btn btn-primary">Save Changes</button>
          <button id="cancel-advanced" class="btn btn-secondary">Cancel</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    this.populateAdvancedSettings(modal);
    this.setupAdvancedSettingsEvents(modal);
  }

  populateAdvancedSettings(modal) {
    // Performance settings
    modal.querySelector('#animation-speed').value = this.settings.animationSpeed || 'normal';
    modal.querySelector('#tutorial-delay').value = this.settings.tutorialDelay || 2000;
    modal.querySelector('#preload-content').checked = this.settings.preloadContent !== false;

    // Privacy settings
    modal.querySelector('#analytics-enabled').checked = this.settings.analyticsEnabled !== false;
    modal.querySelector('#error-reporting').checked = this.settings.errorReportingEnabled !== false;
    modal.querySelector('#usage-data-sharing').checked = this.settings.usageDataSharing !== false;

    // Accessibility settings
    modal.querySelector('#high-contrast').checked = this.settings.highContrast || false;
    modal.querySelector('#reduced-motion').checked = this.settings.reducedMotion || false;
    modal.querySelector('#font-size').value = this.settings.fontSize || 'medium';
  }

  setupAdvancedSettingsEvents(modal) {
    modal.querySelector('.modal-close').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#cancel-advanced').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#save-advanced').addEventListener('click', async () => {
      try {
        const updates = {
          animationSpeed: modal.querySelector('#animation-speed').value,
          tutorialDelay: parseInt(modal.querySelector('#tutorial-delay').value),
          preloadContent: modal.querySelector('#preload-content').checked,
          analyticsEnabled: modal.querySelector('#analytics-enabled').checked,
          errorReportingEnabled: modal.querySelector('#error-reporting').checked,
          usageDataSharing: modal.querySelector('#usage-data-sharing').checked,
          highContrast: modal.querySelector('#high-contrast').checked,
          reducedMotion: modal.querySelector('#reduced-motion').checked,
          fontSize: modal.querySelector('#font-size').value
        };

        await chrome.runtime.sendMessage({
          type: 'UPDATE_SETTINGS',
          settings: updates
        });

        // Update local settings
        Object.assign(this.settings, updates);

        document.body.removeChild(modal);
        this.showSuccess('Advanced settings saved successfully');

      } catch (error) {
        console.error('Failed to save advanced settings:', error);
        this.showError('Failed to save advanced settings');
      }
    });

    // Close on overlay click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  showBackupManager() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content backup-manager-modal">
        <div class="modal-header">
          <h3>Backup Manager</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="backup-actions">
            <button id="create-backup" class="btn btn-primary">Create Backup</button>
            <button id="export-settings" class="btn btn-secondary">Export Settings</button>
            <button id="import-settings" class="btn btn-secondary">Import Settings</button>
          </div>
          <div class="backup-list">
            <h4>Available Backups</h4>
            <div id="backups-container">Loading...</div>
          </div>
          <input type="file" id="import-file" accept=".json" style="display: none;" />
        </div>
        <div class="modal-footer">
          <button id="close-backup" class="btn btn-secondary">Close</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    this.loadBackups(modal);
    this.setupBackupManagerEvents(modal);
  }

  async loadBackups(modal) {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_BACKUPS' });
      const backups = response.backups || [];

      const container = modal.querySelector('#backups-container');

      if (backups.length === 0) {
        container.innerHTML = '<p class="no-backups">No backups available</p>';
        return;
      }

      container.innerHTML = backups.map(backup => `
        <div class="backup-item">
          <div class="backup-info">
            <div class="backup-id">${backup.id}</div>
            <div class="backup-date">${new Date(backup.timestamp).toLocaleString()}</div>
            <div class="backup-version">v${backup.version}</div>
          </div>
          <div class="backup-actions">
            <button class="btn btn-small btn-primary restore-backup" data-backup-id="${backup.id}">Restore</button>
          </div>
        </div>
      `).join('');

      // Add restore event listeners
      container.querySelectorAll('.restore-backup').forEach(button => {
        button.addEventListener('click', async (e) => {
          const backupId = e.target.dataset.backupId;
          await this.restoreBackup(backupId);
        });
      });

    } catch (error) {
      console.error('Failed to load backups:', error);
      modal.querySelector('#backups-container').innerHTML = '<p class="error">Failed to load backups</p>';
    }
  }

  setupBackupManagerEvents(modal) {
    modal.querySelector('.modal-close').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#close-backup').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#create-backup').addEventListener('click', async () => {
      try {
        const label = `manual-${Date.now()}`;
        await chrome.runtime.sendMessage({
          type: 'CREATE_BACKUP',
          label: label
        });

        this.showSuccess('Backup created successfully');
        this.loadBackups(modal); // Refresh backup list

      } catch (error) {
        console.error('Failed to create backup:', error);
        this.showError('Failed to create backup');
      }
    });

    modal.querySelector('#export-settings').addEventListener('click', async () => {
      try {
        const response = await chrome.runtime.sendMessage({ type: 'EXPORT_SETTINGS' });
        const exportData = response.data;

        // Create download
        const blob = new Blob([exportData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `tutorai-settings-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showSuccess('Settings exported successfully');

      } catch (error) {
        console.error('Failed to export settings:', error);
        this.showError('Failed to export settings');
      }
    });

    modal.querySelector('#import-settings').addEventListener('click', () => {
      modal.querySelector('#import-file').click();
    });

    modal.querySelector('#import-file').addEventListener('change', async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        await chrome.runtime.sendMessage({
          type: 'IMPORT_SETTINGS',
          data: text
        });

        // Reload settings
        await this.loadSettings();
        this.updateSettingsUI();

        this.showSuccess('Settings imported successfully');
        document.body.removeChild(modal);

      } catch (error) {
        console.error('Failed to import settings:', error);
        this.showError('Failed to import settings');
      }
    });

    // Close on overlay click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  async restoreBackup(backupId) {
    try {
      await chrome.runtime.sendMessage({
        type: 'RESTORE_BACKUP',
        backupId: backupId
      });

      // Reload settings
      await this.loadSettings();
      this.updateSettingsUI();

      this.showSuccess('Settings restored successfully');

    } catch (error) {
      console.error('Failed to restore backup:', error);
      this.showError('Failed to restore backup');
    }
  }

  confirmResetSettings() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content confirm-modal">
        <div class="modal-header">
          <h3>Reset Settings</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to reset all settings to their default values?</p>
          <p><strong>This action cannot be undone.</strong></p>
          <p>A backup will be created automatically before resetting.</p>
        </div>
        <div class="modal-footer">
          <button id="confirm-reset" class="btn btn-destructive">Reset Settings</button>
          <button id="cancel-reset" class="btn btn-secondary">Cancel</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    modal.querySelector('.modal-close').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#cancel-reset').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.querySelector('#confirm-reset').addEventListener('click', async () => {
      try {
        await chrome.runtime.sendMessage({ type: 'RESET_SETTINGS' });

        // Reload settings
        await this.loadSettings();
        this.updateSettingsUI();

        document.body.removeChild(modal);
        this.showSuccess('Settings reset to defaults');

      } catch (error) {
        console.error('Failed to reset settings:', error);
        this.showError('Failed to reset settings');
      }
    });

    // Close on overlay click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  showSuccess(message) {
    // Create success notification
    const successNotification = document.createElement('div');
    successNotification.className = 'success-notification';
    successNotification.innerHTML = `
      <div class="success-content">
        <span class="success-icon">✅</span>
        <span class="success-message">${message}</span>
        <button class="success-close">&times;</button>
      </div>
    `;

    document.querySelector('.content').appendChild(successNotification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (successNotification.parentNode) {
        successNotification.parentNode.removeChild(successNotification);
      }
    }, 3000);

    // Manual close
    successNotification.querySelector('.success-close').addEventListener('click', () => {
      if (successNotification.parentNode) {
        successNotification.parentNode.removeChild(successNotification);
      }
    });
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new TutorAIPopup();
});
