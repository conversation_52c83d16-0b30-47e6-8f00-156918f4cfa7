#!/bin/bash

# AG-UI Installation Script for TutorAI
echo "🚀 Installing AG-UI Protocol dependencies..."

# Install core dependencies
echo "📦 Installing AG-UI packages..."
npm install @ag-ui/core @ag-ui/client @ag-ui/encoder

# Install AI provider SDKs
echo "🤖 Installing AI provider SDKs..."
npm install openai @anthropic-ai/sdk

# Install additional dependencies
echo "🔧 Installing additional dependencies..."
npm install @types/node

# Fix any potential package issues
echo "🔨 Fixing package issues..."
npm audit fix --force

# Generate types
echo "📝 Generating types..."
npx tsc --noEmit

echo "✅ Installation complete!"
echo ""
echo "📋 Next steps:"
echo "1. Copy .env.example to .env"
echo "2. Add your API keys:"
echo "   - OPENAI_API_KEY=your-openai-key"
echo "   - ANTHROPIC_API_KEY=your-anthropic-key"
echo "3. Run: npm run dev"
echo ""
echo "🎯 Your TutorAI app is now ready with AG-UI Protocol!"
