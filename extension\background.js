/**
 * TutorAI Background Script
 * Handles extension lifecycle, tab management, and cross-tab communication
 */

// Import settings manager and performance monitor
importScripts('settings-manager.js');
importScripts('performance-monitor.js');

class TutorAIBackground {
  constructor() {
    this.activeTabs = new Map();
    this.settings = {};
    this.settingsManager = null;
    this.performanceMonitor = null;
    this.updateChecker = null;
    this.updateCheckInterval = 6 * 60 * 60 * 1000; // 6 hours

    this.init();
  }

  async init() {
    // Initialize settings manager
    this.settingsManager = new TutorAISettingsManager();
    await this.settingsManager.init();

    // Load settings on startup
    await this.loadSettings();

    // Set up event listeners
    this.setupEventListeners();

    // Initialize auto-update system
    this.initializeAutoUpdate();

    console.log('TutorAI: Background script initialized');
  }

  async loadSettings() {
    try {
      this.settings = await this.settingsManager.loadSettings();
    } catch (error) {
      console.error('TutorAI: Failed to load settings:', error);
      // Use defaults if loading fails
      this.settings = this.settingsManager.defaultSettings;
    }
  }

  setupEventListeners() {
    // Extension installation/update
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Tab events
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.handleTabActivated(activeInfo);
    });

    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });

    chrome.tabs.onRemoved.addListener((tabId) => {
      this.handleTabRemoved(tabId);
    });

    // Message handling
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Context menu
    this.setupContextMenu();

    // Keyboard shortcuts
    chrome.commands.onCommand.addListener((command) => {
      this.handleCommand(command);
    });
  }

  handleInstallation(details) {
    if (details.reason === 'install') {
      // First installation
      this.showWelcomePage();
      this.setDefaultSettings();
    } else if (details.reason === 'update') {
      // Extension update
      this.handleUpdate(details.previousVersion);
    }
  }

  showWelcomePage() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/welcome?source=extension`
    });
  }

  setDefaultSettings() {
    chrome.storage.sync.set({
      tutorAISettings: this.settings
    });
  }

  handleUpdate(previousVersion) {
    console.log(`TutorAI: Updated from version ${previousVersion}`);
    // Handle any migration logic here
  }

  handleTabActivated(activeInfo) {
    const tabId = activeInfo.tabId;
    
    // Update active tab tracking
    this.activeTabs.set(tabId, {
      id: tabId,
      active: true,
      lastActivated: Date.now()
    });

    // Auto-start if enabled
    if (this.settings.autoStart) {
      this.autoStartTutorial(tabId);
    }
  }

  async handleTabUpdated(tabId, changeInfo, tab) {
    // Only process when page is completely loaded
    if (changeInfo.status !== 'complete') return;

    // Skip chrome:// and extension pages
    if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
      return;
    }

    // Update tab info
    this.activeTabs.set(tabId, {
      id: tabId,
      url: tab.url,
      title: tab.title,
      lastUpdated: Date.now()
    });

    // Auto-start if enabled and this is the active tab
    if (this.settings.autoStart && tab.active) {
      setTimeout(() => {
        this.autoStartTutorial(tabId);
      }, 2000); // Wait 2 seconds for page to settle
    }
  }

  handleTabRemoved(tabId) {
    this.activeTabs.delete(tabId);
  }

  async handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'GET_SETTINGS':
        sendResponse(this.settings);
        break;

      case 'UPDATE_SETTINGS':
        await this.updateSettings(message.settings);
        sendResponse({ success: true });
        break;

      case 'UPDATE_SETTING':
        await this.updateSetting(message.key, message.value);
        sendResponse({ success: true });
        break;

      case 'RESET_SETTINGS':
        await this.resetSettings();
        sendResponse({ success: true });
        break;

      case 'EXPORT_SETTINGS':
        const exportData = await this.exportSettings();
        sendResponse({ success: true, data: exportData });
        break;

      case 'IMPORT_SETTINGS':
        await this.importSettings(message.data);
        sendResponse({ success: true });
        break;

      case 'CREATE_BACKUP':
        const backupId = await this.createBackup(message.label);
        sendResponse({ success: true, backupId });
        break;

      case 'GET_BACKUPS':
        const backups = await this.getBackups();
        sendResponse({ success: true, backups });
        break;

      case 'RESTORE_BACKUP':
        await this.restoreBackup(message.backupId);
        sendResponse({ success: true });
        break;

      case 'START_TUTORIAL':
        await this.startTutorialOnTab(sender.tab.id, message.options);
        sendResponse({ success: true });
        break;

      case 'GET_TAB_INFO':
        const tabInfo = this.activeTabs.get(sender.tab.id);
        sendResponse(tabInfo);
        break;

      case 'REPORT_ERROR':
        this.reportError(message.error, sender.tab);
        break;

      case 'INSTALL_UPDATE':
        await this.initiateUpdate(message.data);
        sendResponse({ success: true });
        break;

      default:
        console.warn('TutorAI: Unknown message type:', message.type);
    }
  }

  async updateSettings(newSettings) {
    try {
      this.settings = await this.settingsManager.updateSettings(newSettings);
    } catch (error) {
      console.error('TutorAI: Failed to update settings:', error);
      throw error;
    }
  }

  async updateSetting(key, value) {
    try {
      this.settings = await this.settingsManager.updateSetting(key, value);
    } catch (error) {
      console.error('TutorAI: Failed to update setting:', error);
      throw error;
    }
  }

  async resetSettings() {
    try {
      this.settings = await this.settingsManager.resetSettings();
    } catch (error) {
      console.error('TutorAI: Failed to reset settings:', error);
      throw error;
    }
  }

  async exportSettings() {
    try {
      return await this.settingsManager.exportSettings();
    } catch (error) {
      console.error('TutorAI: Failed to export settings:', error);
      throw error;
    }
  }

  async importSettings(importData) {
    try {
      this.settings = await this.settingsManager.importSettings(importData);
    } catch (error) {
      console.error('TutorAI: Failed to import settings:', error);
      throw error;
    }
  }

  async createBackup(label) {
    try {
      return await this.settingsManager.createBackup(null, label);
    } catch (error) {
      console.error('TutorAI: Failed to create backup:', error);
      throw error;
    }
  }

  async getBackups() {
    try {
      return await this.settingsManager.getBackups();
    } catch (error) {
      console.error('TutorAI: Failed to get backups:', error);
      throw error;
    }
  }

  async restoreBackup(backupId) {
    try {
      this.settings = await this.settingsManager.restoreBackup(backupId);
    } catch (error) {
      console.error('TutorAI: Failed to restore backup:', error);
      throw error;
    }
  }

  setupContextMenu() {
    chrome.contextMenus.create({
      id: 'tutorai-explain',
      title: 'Explain with TutorAI',
      contexts: ['all']
    });

    chrome.contextMenus.create({
      id: 'tutorai-tutorial',
      title: 'Start Tutorial',
      contexts: ['page']
    });

    chrome.contextMenus.onClicked.addListener((info, tab) => {
      this.handleContextMenuClick(info, tab);
    });
  }

  async handleContextMenuClick(info, tab) {
    switch (info.menuItemId) {
      case 'tutorai-explain':
        await this.explainSelection(tab.id, info);
        break;

      case 'tutorai-tutorial':
        await this.startTutorialOnTab(tab.id);
        break;
    }
  }

  async explainSelection(tabId, info) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: 'EXPLAIN_SELECTION',
        selectionText: info.selectionText,
        pageUrl: info.pageUrl
      });
    } catch (error) {
      console.error('Failed to explain selection:', error);
    }
  }

  async startTutorialOnTab(tabId, options = {}) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: 'START_TUTORIAL',
        data: options
      });
    } catch (error) {
      console.error('Failed to start tutorial:', error);
      // Content script might not be loaded, try injecting it
      await this.injectContentScript(tabId);
    }
  }

  async autoStartTutorial(tabId) {
    if (!this.settings.enabled || !this.settings.autoStart) return;

    try {
      const tab = await chrome.tabs.get(tabId);
      
      // Skip certain URLs
      if (this.shouldSkipAutoStart(tab.url)) return;

      await this.startTutorialOnTab(tabId, {
        auto: true,
        query: 'Welcome! I can help you understand this webpage. Click on any element to learn more about it.'
      });
    } catch (error) {
      console.error('Auto-start tutorial failed:', error);
    }
  }

  shouldSkipAutoStart(url) {
    const skipPatterns = [
      'chrome://',
      'chrome-extension://',
      'about:',
      'moz-extension://',
      'file://',
      'localhost:3000' // Skip TutorAI's own pages
    ];

    return skipPatterns.some(pattern => url.startsWith(pattern));
  }

  async injectContentScript(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content-script.js']
      });

      await chrome.scripting.insertCSS({
        target: { tabId: tabId },
        files: ['content-styles.css']
      });
    } catch (error) {
      console.error('Failed to inject content script:', error);
    }
  }

  async handleCommand(command) {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    switch (command) {
      case 'start-tutorial':
        await this.startTutorialOnTab(tab.id);
        break;

      case 'toggle-highlighting':
        await chrome.tabs.sendMessage(tab.id, {
          type: 'TOGGLE_HIGHLIGHTING'
        });
        break;
    }
  }

  reportError(error, tab) {
    console.error('TutorAI Error:', error);

    // Send error to analytics if configured
    if (this.settings.tutorAIServer) {
      fetch(`${this.settings.tutorAIServer}/api/analytics/error`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: error,
          tab: {
            url: tab.url,
            title: tab.title
          },
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        })
      }).catch(err => {
        console.error('Failed to report error:', err);
      });
    }
  }

  // ===== AUTO-UPDATE SYSTEM =====

  initializeAutoUpdate() {
    // Check for updates on startup
    this.checkForUpdates();

    // Set up periodic update checks
    this.updateChecker = setInterval(() => {
      this.checkForUpdates();
    }, this.updateCheckInterval);

    // Listen for extension management events
    if (chrome.management) {
      chrome.management.onEnabled.addListener((info) => {
        if (info.id === chrome.runtime.id) {
          this.handleExtensionEnabled();
        }
      });

      chrome.management.onDisabled.addListener((info) => {
        if (info.id === chrome.runtime.id) {
          this.handleExtensionDisabled();
        }
      });
    }

    // Listen for runtime update events
    chrome.runtime.onUpdateAvailable.addListener((details) => {
      this.handleUpdateAvailable(details);
    });

    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'update') {
        this.handleUpdateInstalled(details.previousVersion);
      }
    });
  }

  async checkForUpdates() {
    try {
      const manifest = chrome.runtime.getManifest();
      const currentVersion = manifest.version;

      // Check server for latest version
      const response = await fetch(`${this.settings.tutorAIServer}/api/extension/version`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Extension-Version': currentVersion,
          'X-Extension-ID': chrome.runtime.id
        }
      });

      if (!response.ok) {
        console.warn('TutorAI: Failed to check for updates:', response.statusText);
        return;
      }

      const versionInfo = await response.json();

      if (this.isNewerVersion(versionInfo.latestVersion, currentVersion)) {
        await this.notifyUpdateAvailable(versionInfo);
      }

      // Report current version to analytics
      this.reportVersionInfo(currentVersion, versionInfo);

    } catch (error) {
      console.error('TutorAI: Update check failed:', error);
    }
  }

  isNewerVersion(latest, current) {
    const latestParts = latest.split('.').map(Number);
    const currentParts = current.split('.').map(Number);

    for (let i = 0; i < Math.max(latestParts.length, currentParts.length); i++) {
      const latestPart = latestParts[i] || 0;
      const currentPart = currentParts[i] || 0;

      if (latestPart > currentPart) return true;
      if (latestPart < currentPart) return false;
    }

    return false;
  }

  async notifyUpdateAvailable(versionInfo) {
    const { latestVersion, releaseNotes, isSecurityUpdate, downloadUrl } = versionInfo;

    // Create notification
    if (chrome.notifications) {
      const notificationId = `update-${Date.now()}`;

      await chrome.notifications.create(notificationId, {
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'TutorAI Update Available',
        message: `Version ${latestVersion} is available. ${isSecurityUpdate ? '🔒 Security update recommended.' : ''}`,
        buttons: [
          { title: 'Update Now' },
          { title: 'Later' }
        ],
        requireInteraction: isSecurityUpdate
      });

      // Handle notification clicks
      chrome.notifications.onButtonClicked.addListener((notifId, buttonIndex) => {
        if (notifId === notificationId) {
          if (buttonIndex === 0) {
            this.initiateUpdate(versionInfo);
          }
          chrome.notifications.clear(notifId);
        }
      });
    }

    // Store update info for popup display
    await chrome.storage.local.set({
      pendingUpdate: {
        version: latestVersion,
        releaseNotes,
        isSecurityUpdate,
        downloadUrl,
        timestamp: Date.now()
      }
    });

    // Notify all tabs about available update
    this.broadcastToAllTabs({
      type: 'UPDATE_AVAILABLE',
      data: versionInfo
    });
  }

  async initiateUpdate(versionInfo) {
    try {
      // Show loading state
      this.broadcastToAllTabs({
        type: 'UPDATE_STARTING',
        data: { version: versionInfo.latestVersion }
      });

      // For Chrome Web Store extensions, request update
      if (chrome.runtime.requestUpdateCheck) {
        chrome.runtime.requestUpdateCheck((status, details) => {
          if (status === 'update_available') {
            chrome.runtime.reload();
          } else if (status === 'no_update') {
            console.log('TutorAI: No update available through Chrome Web Store');
          }
        });
      }

      // For development/enterprise extensions, handle manual update
      if (versionInfo.downloadUrl) {
        chrome.tabs.create({ url: versionInfo.downloadUrl });
      }

    } catch (error) {
      console.error('TutorAI: Update initiation failed:', error);
      this.broadcastToAllTabs({
        type: 'UPDATE_FAILED',
        data: { error: error.message }
      });
    }
  }

  handleUpdateAvailable(details) {
    console.log('TutorAI: Chrome detected update available:', details);

    // Store update details
    chrome.storage.local.set({
      chromeUpdateAvailable: {
        version: details.version,
        timestamp: Date.now()
      }
    });

    // Reload extension to apply update
    chrome.runtime.reload();
  }

  async handleUpdateInstalled(previousVersion) {
    console.log(`TutorAI: Updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);

    // Clear pending update info
    await chrome.storage.local.remove(['pendingUpdate', 'chromeUpdateAvailable']);

    // Perform migration if needed
    await this.performUpdateMigration(previousVersion);

    // Show update success notification
    if (chrome.notifications) {
      chrome.notifications.create(`update-success-${Date.now()}`, {
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'TutorAI Updated Successfully',
        message: `Updated to version ${chrome.runtime.getManifest().version}`,
        buttons: [{ title: 'View Changes' }]
      });
    }

    // Report successful update
    this.reportUpdateSuccess(previousVersion);

    // Broadcast update completion
    this.broadcastToAllTabs({
      type: 'UPDATE_COMPLETED',
      data: {
        previousVersion,
        currentVersion: chrome.runtime.getManifest().version
      }
    });
  }

  async performUpdateMigration(previousVersion) {
    try {
      // Get current settings
      const result = await chrome.storage.sync.get(['tutorAISettings']);
      let settings = result.tutorAISettings || {};

      // Version-specific migrations
      if (this.isNewerVersion('1.1.0', previousVersion)) {
        // Migration for v1.1.0
        settings.newFeatureEnabled = true;
        settings.migrationVersion = '1.1.0';
      }

      if (this.isNewerVersion('1.2.0', previousVersion)) {
        // Migration for v1.2.0
        if (settings.highlightColor === '#3b82f6') {
          settings.highlightColor = '#2563eb'; // Updated default color
        }
        settings.migrationVersion = '1.2.0';
      }

      // Save migrated settings
      await chrome.storage.sync.set({ tutorAISettings: settings });

      console.log('TutorAI: Migration completed successfully');

    } catch (error) {
      console.error('TutorAI: Migration failed:', error);
    }
  }

  async reportVersionInfo(currentVersion, versionInfo) {
    try {
      await fetch(`${this.settings.tutorAIServer}/api/analytics/extension-version`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          extensionId: chrome.runtime.id,
          currentVersion,
          latestVersion: versionInfo.latestVersion,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('TutorAI: Failed to report version info:', error);
    }
  }

  async reportUpdateSuccess(previousVersion) {
    try {
      await fetch(`${this.settings.tutorAIServer}/api/analytics/extension-update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          extensionId: chrome.runtime.id,
          previousVersion,
          currentVersion: chrome.runtime.getManifest().version,
          updateMethod: 'auto',
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('TutorAI: Failed to report update success:', error);
    }
  }

  handleExtensionEnabled() {
    console.log('TutorAI: Extension enabled');
    this.checkForUpdates();
  }

  handleExtensionDisabled() {
    console.log('TutorAI: Extension disabled');
    if (this.updateChecker) {
      clearInterval(this.updateChecker);
    }
  }

  async broadcastToAllTabs(message) {
    try {
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, message);
        } catch (error) {
          // Tab might not have content script, ignore
        }
      }
    } catch (error) {
      console.error('TutorAI: Failed to broadcast message:', error);
    }
  }
}

// Initialize background script
new TutorAIBackground();
